package com.subfg.subfgapi.Serivce;

import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.subfg.common.constans.RedisConstants;
import com.subfg.common.exception.BusinessException;
import com.subfg.common.util.IdGeneratorUtil;
import com.subfg.common.util.RedisUtil;
import com.subfg.common.util.TimeUtil;
import com.subfg.common.util.VerifyCodeUtil;
import com.subfg.domain.dto.wx.WxAccessTokenDto;
import com.subfg.domain.dto.wx.WxUserDetailDto;
import com.subfg.domain.entity.user.UserFundPo;
import com.subfg.domain.entity.user.UserPo;
import com.subfg.domain.request.LoginReq;
import com.subfg.domain.request.RegisterReq;
import com.subfg.domain.request.SendEmailCodeReq;
import com.subfg.domain.vo.LoginVo;
import com.subfg.repository.mapper.UserAuthMapper;
import com.subfg.repository.mapper.UserFundMapper;
import com.subfg.repository.mapper.UserMapper;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.digest.DigestUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {

    private final UserMapper userMapper;
    private final UserAuthMapper userAuthMapper;
    private final RedisUtil redisUtil;
    private final EmailService emailService;
    private final WxService wxService;
    private final UserFundMapper userFundMapper;


    /**
     * 发送邮箱验证码
     */
    public void sendEmailCode(SendEmailCodeReq req){
        // 1. 验证邮箱是否存在
        UserPo user = userMapper.selectOne(new LambdaQueryWrapper<UserPo>().eq(UserPo::getEmail, req.getEmail()));
        if (user == null) {
            throw new BusinessException("user.not.exists");
        }

        // 2. 检查发送频率限制
        String redisKey = RedisConstants.EMAIL_CODE_KEY + req.getEmail();
        String existingCode = redisUtil.getString(redisKey);
        if (existingCode != null) {
            // 获取剩余过期时间
            Long expireTime = redisUtil.getExpire(redisKey, TimeUnit.SECONDS);
            if (expireTime != null && expireTime > (RedisConstants.EMAIL_CODE_EXPIRE_MINUTES * 60 - RedisConstants.EMAIL_CODE_SEND_INTERVAL_SECONDS)) {
                throw new BusinessException("email.code.send.too.frequent");
            }
        }

        // 3. 生成并存储验证码
        String code = VerifyCodeUtil.generateNumberCode(6);
        redisUtil.set(redisKey, code, RedisConstants.EMAIL_CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        log.debug("验证码已存储 - 邮箱: {}, 验证码: {}", req.getEmail(), code);

        // 4. 异步发送邮件
        try {
            emailService.sendVerifyCodeEmail(req.getEmail(), code, req.getType());
            log.info("验证码发送成功 - 邮箱: {}, 类型: {}", req.getEmail(), req.getType());
        } catch (Exception e) {
            log.error("验证码邮件发送失败 - 邮箱: {}", req.getEmail(), e);
            throw new BusinessException("email.send.failed");
        }
    }

    /**
     * 用户登录
     */
    public LoginVo login(LoginReq req){
        // 1. 验证邮箱是否存在
        UserPo user = userMapper.selectOne(new LambdaQueryWrapper<UserPo>().eq(UserPo::getEmail, req.getEmail()));
        if (user == null) {
            throw new BusinessException("user.not.exists");
        }

        // 2. 验证用户是否被禁用
        if (!user.getEnable()) {
            throw new BusinessException("user.disabled");
        }

        // 3. 验证密码是否正确（使用MD5+盐值验证）
        String inputPasswordHash = DigestUtil.md5Hex(req.getPassword() + user.getSalt());
        if (!user.getPassword().equals(inputPasswordHash)) {
            throw new BusinessException("password.incorrect");
        }

        // 4. 生成token
        StpUtil.login(user.getUserId());
        String token = StpUtil.getTokenInfo().getTokenValue();

        // 7. 更新用户最后在线时间
        user.setLastOnlineTime(TimeUtil.getCurrentTimestamp());
        userMapper.updateById(user);

        // 8. 构建返回对象
        LoginVo loginVO = new LoginVo();
        loginVO.setUser(user);
        loginVO.setToken(token);
        loginVO.setExpireTime(StpUtil.getTokenInfo().getTokenTimeout());

        log.info("用户登录成功 - 用户ID: {}, 邮箱: {}", user.getUserId(), user.getEmail());

        return loginVO;
    }

    /**
     * 微信登录回调
     */
    public LoginVo wxLoginCallback(String code){
        WxAccessTokenDto wxAccessTokenDto = wxService.getWxAccessToken(code);
        WxUserDetailDto wxUserDetailDto = wxService.getWxUserInfo(wxAccessTokenDto.getAccess_token(), wxAccessTokenDto.getOpenid());

        // 1. 验证用户是否存在 不存在则注册用户
        UserPo userPo = userMapper.selectOne(new LambdaQueryWrapper<UserPo>().eq(UserPo::getWechat, wxUserDetailDto.getUnionid()));
        if(userPo == null){
            // 注册用户
            String salt = RandomUtil.randomString(6);
            userPo = new UserPo()
            .setUserId(IdGeneratorUtil.generateUserId())
            .setUserName(wxUserDetailDto.getNickName())
            .setWechat(wxUserDetailDto.getUnionid())
            .setAvatarUrl(wxUserDetailDto.getHeadimgurl())
            .setEnable(true)
            .setSalt(salt)
            .setCreateTime(TimeUtil.getCurrentTimestamp());
            userMapper.insert(userPo);

            // 用户钱包
        }

        return new LoginVo().setUser(userPo).setToken(StpUtil.getTokenInfo().getTokenValue()).setExpireTime(StpUtil.getTokenInfo().getTokenTimeout());
        
        

        

    }

    /**
     * 用户注册 -- 邮箱
     */
    public void register(RegisterReq req){

        // 1. 验证用户是否存在
        UserPo user = userMapper.selectOne(new LambdaQueryWrapper<UserPo>().eq(UserPo::getEmail, req.getEmail()));
        if (user != null) {
            throw new BusinessException("user.already.exists");
        }

        // 2. 验证验证码是否正确
        String redisKey = RedisConstants.EMAIL_CODE_KEY + req.getEmail();
        String existingCode = redisUtil.getString(redisKey);
        if (existingCode == null || !existingCode.equals(req.getCode())) {
            throw new BusinessException("user.code.error");
        }
        
        // 注册用户
        String salt = RandomUtil.randomString(6);
        UserPo userPo = new UserPo()
        .setUserId(IdGeneratorUtil.generateUserId())
        .setUserName(req.getUserName())
        .setEmail(req.getEmail())
        .setEnable(true)
        .setPassword(DigestUtil.md5Hex(req.getPassword() + salt))
        .setCreditScore(new BigDecimal(100))
        .setSalt(salt)
        .setCreateTime(TimeUtil.getCurrentTimestamp());

        userMapper.insert(userPo);

        // 用户钱包
        UserFundPo userFundPo = new UserFundPo()
        .setUserId(userPo.getUserId())
        .setTotalAmount(BigDecimal.ZERO)
        .setWithdrawAmount(BigDecimal.ZERO)
        .setCurrencyType("CNY")
        .setTotalIncome(BigDecimal.ZERO)
        .setTotalExpense(BigDecimal.ZERO)
        .setCreateBookingCount(0)
        .setCustomSubMCount(0)
        .setCreateFgCount(0)
        .setJoinFgCount(0);
        userFundMapper.insert(userFundPo);


        redisUtil.delete(redisKey);
    }

}
