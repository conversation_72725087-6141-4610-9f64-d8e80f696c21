package com.subfg.subfgapi.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.subfg.domain.request.LoginReq;
import com.subfg.domain.request.RegisterReq;
import com.subfg.domain.request.SendEmailCodeReq;
import com.subfg.domain.vo.LoginVo;
import com.subfg.domain.vo.Result;
import com.subfg.subfgapi.Serivce.AuthService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v3/auth")
@Tag(name = "认证管理", description = "认证相关接口")
public class AuthController {

    private final AuthService authService;

    /**
     * 发送邮箱验证码
     */
    @PostMapping("/sendEmailCode")
    @Operation(summary = "发送邮箱验证码", description = "发送邮箱验证码")
    public Result<String> sendEmailCode(@RequestBody SendEmailCodeReq req){
        authService.sendEmailCode(req);
        return Result.successI18n("auth.sendEmailCode.success");
    }

    /**
     * 微信登录回调 用户扫码后前端拿到code回调此接口
     */
    @GetMapping("/wxLoginCallback")
    @Operation(summary = "微信登录回调", description = "微信登录回调")
    public Result wxLoginCallback(@RequestParam String code){
        authService.wxLoginCallback(code);
        return Result.success("微信登录回调");
    }

    /**
     * 用户登录 邮箱-密码
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户登录")
    public Result<LoginVo> login(@RequestBody LoginReq req){
        return Result.success(authService.login(req));
    }

    /**
     * 用户注册(仅支持邮箱注册)
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "用户注册")
    public Result register(@RequestBody RegisterReq req){
        authService.register(req);
        return Result.successI18n("user.register.success");
    }
    
}
